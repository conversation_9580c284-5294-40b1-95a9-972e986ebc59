using UnityEngine;
using Game.Interfaces;

namespace Game.Objects
{
    /// <summary>
    /// Represents an animated prop object in the game.
    /// Inherits animation capabilities from BaseObject.
    /// </summary>
    public class AnimationProp : BaseObject, IFocusable
    {
        /// <summary>
        /// Gets the transform that the camera should focus on.
        /// </summary>
        public Transform FocusTransform => transform;
    }
}