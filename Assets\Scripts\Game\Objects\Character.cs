using System.Collections;
using UnityEngine;
using Game.Interfaces;

namespace Game.Objects
{
    /// <summary>
    /// Represents a character in the game with animation and dialogue capabilities.
    /// </summary>
    public class Character : BaseObject, IFocusable
    {
        [SerializeField]
        [Tooltip("Transform representing the character's hand for object attachment")]
        private Transform handTransform;

        [Serial<PERSON>Field]
        [Tooltip("AudioSource component for playing dialogue audio")]
        private AudioSource dialogueAudioSource;

        [SerializeField]
        [<PERSON>lt<PERSON>("Voice instructions for TTS generation")]
        private string voiceInstructions;

        [SerializeField]
        [Tooltip("Voice actor name for TTS generation")]
        private string voiceActor;

        [SerializeField]
        [Tooltip("Speed multiplier for voice playback")]
        private float voiceSpeed = 1.0f;

        private Coroutine talkCoroutine;
        private readonly string talkAnimationName = "Talk";
        private readonly string silentAnimationName = "Silent";
        private readonly int talkAnimationCount = 3;
        private readonly string talkingExpressionAnimationName = "TalkingExpression";
        private readonly int talkingExpressionAnimationCount = 3;
        private readonly float minTalkingExpressionCooldown = 0f;
        private readonly float maxTalkingExpressionCooldown = 3f;

        /// <summary>
        /// Gets the AudioSource used for dialogue playback.
        /// </summary>
        public AudioSource DialogueAudioSource => dialogueAudioSource;

        /// <summary>
        /// Gets the transform representing the character's hand for object attachment.
        /// </summary>
        public Transform HandTransform => handTransform;

        /// <summary>
        /// Gets the voice instructions for this character.
        /// </summary>
        public string VoiceInstructions => voiceInstructions;

        /// <summary>
        /// Gets the name of the voice actor for this character.
        /// </summary>
        public string VoiceActor => voiceActor;

        /// <summary>
        /// Gets the speed of the voice playback for this character.
        /// </summary>
        public float VoiceSpeed => voiceSpeed;

        /// <summary>
        /// Gets the transform that the camera should focus on.
        /// </summary>
        public Transform FocusTransform => transform;

        /// <summary>
        /// Plays dialogue audio and starts talking animations.
        /// </summary>
        /// <param name="audio">The audio clip to play.</param>
        public void PlayDialogue(AudioClip audio)
        {
            if (audio == null)
            {
                Debug.LogWarning($"[{nameof(Character)}] Audio clip is null for dialogue.");
                return;
            }

            if (DialogueAudioSource == null)
            {
                Debug.LogWarning($"[{nameof(Character)}] Dialogue audio source is not assigned.");
                return;
            }

            DialogueAudioSource.clip = audio;
            DialogueAudioSource.Play();

            if (talkCoroutine != null)
            {
                StopCoroutine(talkCoroutine);
            }
            talkCoroutine = StartCoroutine(TalkCoroutine(audio.length));
        }

        /// <summary>
        /// Stops the current dialogue and returns to silent animation.
        /// </summary>
        public void StopDialogue()
        {
            if (DialogueAudioSource != null && DialogueAudioSource.isPlaying)
            {
                DialogueAudioSource.Stop();

                if (talkCoroutine != null)
                {
                    StopCoroutine(talkCoroutine);
                    PlayAnimation(silentAnimationName);
                }
            }
        }

        /// <summary>
        /// Attaches an object to the character's hand transform.
        /// </summary>
        /// <param name="obj">The object to attach.</param>
        /// <param name="resetLocalPos">Whether to reset the local position to zero.</param>
        /// <param name="resetLocalRot">Whether to reset the local rotation to identity.</param>
        public void AttachObjectToHand(GameObject obj, bool resetLocalPos = true, bool resetLocalRot = false)
        {
            if (obj == null)
            {
                Debug.LogWarning($"[{nameof(Character)}] Cannot attach null object to hand.");
                return;
            }

            if (handTransform == null)
            {
                Debug.LogWarning($"[{nameof(Character)}] Hand transform is not assigned.");
                return;
            }

            obj.transform.SetParent(handTransform);

            if (resetLocalPos)
                obj.transform.localPosition = Vector3.zero;

            if (resetLocalRot)
                obj.transform.localRotation = Quaternion.identity;
        }

        private void Awake()
        {
            if (dialogueAudioSource != null)
            {
                dialogueAudioSource.loop = false;
                dialogueAudioSource.playOnAwake = false;
                dialogueAudioSource.Stop();
            }
        }

        private IEnumerator TalkCoroutine(float duration)
        {
            // Play initial talk animation
            int randomTalkId = Random.Range(0, talkAnimationCount);
            string finalTalkAnimation = $"{talkAnimationName}{randomTalkId}";
            PlayAnimation(finalTalkAnimation);

            float elapsedTime = 0f;
            float lastExpressionTime = 0f;
            float nextExpressionTime = Random.Range(minTalkingExpressionCooldown, maxTalkingExpressionCooldown);

            // Continue playing talking expression animations during the dialogue
            while (elapsedTime < duration)
            {
                yield return null; // Wait one frame
                elapsedTime += Time.deltaTime;

                // Check if it's time for the next expression animation
                if (elapsedTime - lastExpressionTime >= nextExpressionTime)
                {
                    // Play random talking expression animation
                    int randomTalkingExpressionId = Random.Range(0, talkingExpressionAnimationCount);
                    string finalTalkingExpressionAnimation = $"{talkingExpressionAnimationName}{randomTalkingExpressionId}";
                    PlayAnimation(finalTalkingExpressionAnimation);

                    lastExpressionTime = elapsedTime;
                    // Set random time for next expression animation
                    nextExpressionTime = Random.Range(minTalkingExpressionCooldown, maxTalkingExpressionCooldown);
                }
            }

            // Return to silent animation when dialogue ends
            PlayAnimation(silentAnimationName);
        }
    }
}